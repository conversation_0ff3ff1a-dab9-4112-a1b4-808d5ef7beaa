"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "ui/card";

export function DebugAudioVolume() {
  const [isListening, setIsListening] = useState(false);
  const [audioVolume, setAudioVolume] = useState(1.0);
  const [logs, setLogs] = useState<string[]>([]);
  const audioElementRef = useRef<HTMLAudioElement | null>(null);
  const audioStreamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-20), `${timestamp}: ${message}`]);
  };

  const createAudioElement = () => {
    if (!audioElementRef.current) {
      audioElementRef.current = document.createElement("audio");
      audioElementRef.current.autoplay = true;
      audioElementRef.current.volume = 1.0;
      audioElementRef.current.muted = false;
      
      // Add event listeners to monitor volume changes
      audioElementRef.current.addEventListener('volumechange', () => {
        const volume = audioElementRef.current?.volume || 0;
        setAudioVolume(volume);
        addLog(`Volume changed to: ${volume}`);
      });
      
      audioElementRef.current.addEventListener('loadstart', () => {
        addLog('Audio loadstart event');
      });
      
      audioElementRef.current.addEventListener('canplay', () => {
        addLog('Audio canplay event');
      });
      
      audioElementRef.current.addEventListener('playing', () => {
        addLog('Audio playing event');
      });
      
      document.body.appendChild(audioElementRef.current);
      addLog('Audio element created and added to DOM');
    }
  };

  const startListening = async () => {
    try {
      addLog('Starting microphone...');
      
      if (!audioContextRef.current) {
        audioContextRef.current = new AudioContext();
        addLog(`Audio context created, state: ${audioContextRef.current.state}`);
      }
      
      if (!audioStreamRef.current) {
        audioStreamRef.current = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            autoGainControl: false,
            noiseSuppression: false,
            sampleRate: 48000,
            channelCount: 1,
          }
        });
        addLog('Microphone stream obtained with disabled audio processing');
      }
      
      createAudioElement();
      setIsListening(true);
      addLog('Microphone started');
      
      // Monitor audio element volume
      const monitorVolume = () => {
        if (audioElementRef.current) {
          const currentVolume = audioElementRef.current.volume;
          if (currentVolume !== 1.0) {
            addLog(`⚠️ Volume drift detected: ${currentVolume}, resetting to 1.0`);
            audioElementRef.current.volume = 1.0;
          }
        }
      };
      
      const intervalId = setInterval(monitorVolume, 500);
      
      // Clean up interval after 30 seconds
      setTimeout(() => {
        clearInterval(intervalId);
        addLog('Volume monitoring stopped');
      }, 30000);
      
    } catch (error) {
      addLog(`Error starting microphone: ${error}`);
    }
  };

  const stopListening = () => {
    try {
      addLog('Stopping microphone...');
      
      if (audioStreamRef.current) {
        audioStreamRef.current.getAudioTracks().forEach(track => {
          track.enabled = false;
          addLog(`Track disabled: ${track.label}`);
        });
      }
      
      setIsListening(false);
      addLog('Microphone stopped');
      
      // Force volume maintenance
      setTimeout(() => {
        if (audioElementRef.current) {
          const volume = audioElementRef.current.volume;
          addLog(`Volume after mute: ${volume}`);
          if (volume !== 1.0) {
            audioElementRef.current.volume = 1.0;
            addLog('Volume reset to 1.0 after mute');
          }
        }
      }, 100);
      
    } catch (error) {
      addLog(`Error stopping microphone: ${error}`);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const testVolumeChange = () => {
    if (audioElementRef.current) {
      const newVolume = Math.random();
      audioElementRef.current.volume = newVolume;
      addLog(`Manually set volume to: ${newVolume}`);
    }
  };

  useEffect(() => {
    return () => {
      if (audioStreamRef.current) {
        audioStreamRef.current.getTracks().forEach(track => track.stop());
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
      if (audioElementRef.current && document.body.contains(audioElementRef.current)) {
        document.body.removeChild(audioElementRef.current);
      }
    };
  }, []);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Audio Volume Debug Tool</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={isListening ? stopListening : startListening}
            variant={isListening ? "destructive" : "default"}
          >
            {isListening ? "Stop Microphone" : "Start Microphone"}
          </Button>
          <Button onClick={testVolumeChange} variant="outline">
            Test Volume Change
          </Button>
          <Button onClick={clearLogs} variant="outline">
            Clear Logs
          </Button>
        </div>
        
        <div className="space-y-2">
          <div>Status: {isListening ? "🎤 Listening" : "🔇 Muted"}</div>
          <div>Audio Volume: {audioVolume.toFixed(2)}</div>
          <div>Audio Context State: {audioContextRef.current?.state || "Not created"}</div>
        </div>
        
        <div className="border rounded p-4 h-64 overflow-y-auto bg-gray-50">
          <div className="text-sm font-mono">
            {logs.length === 0 ? (
              <div className="text-gray-500">No logs yet...</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </div>
              
      <div className="text-sm text-gray-600">
        <p><strong>Instructions:</strong></p>
        <ol className="list-decimal list-inside space-y-1">
          <li>Click &quot;Start Microphone&quot; to begin audio capture</li>
          <li>Watch the logs for any volume changes</li>
          <li>Click &quot;Stop Microphone&quot; to mute and observe volume behavior</li>
          <li>Toggle between start/stop multiple times to reproduce the issue</li>
          <li>Use &quot;Test Volume Change&quot; to manually trigger volume events</li>
        </ol>
      </div>
      </CardContent>
    </Card>
  );
}
